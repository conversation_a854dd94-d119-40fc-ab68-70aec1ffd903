<template>
  <section class="live_chat_header" v-if="appCode !== '2'">
    <div class="header_wrap">
      <div class="header_cs_status" :class="`header_cs_status_${csStatus}`">
        {{ $c("popupTitle") }}
      </div>
      <div class="header_right">
        <Popover placement="bottom-right" v-model="popoverVisible">
          <template #trigger>
            <div class="icon_wrap">
              <span class="iconfont iconfont_minimize">&#xe6a6;</span>
            </div>
          </template>
          <template #default>
            <div class="title_list">
              <div v-for="item in htmlList" :key="item.type" class="title_item" @click="handleClick(item.type)">
                <span class="iconfont" v-html="item.icon"></span>
                <div class="title_item_des">
                  {{ item.des }}
                </div>
              </div>
            </div>
          </template>
        </Popover>
        <div class="icon_wrap close_icon_wrap" @click="hideChat">
          <span class="iconfont iconfont_close">&#xf30a;</span>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import Popover from "@/components/Popover.vue";
import { ref, inject } from "vue";
import EventBus from "@/util/eventBus";
import { $c } from "@/plugins/c-inject";
import { initSocket } from '@/plugins/socket';

const $bus = inject<EventBus>("eventBus");
const ws = initSocket();

const popoverVisible = ref(false);
const emit = defineEmits(["closeChat", "showAddress", "showPastConversations"]);

const htmlList = ref([
  {
    type: 1,
    icon: "&#xe6a5;",
    des: $c("emailTranscript"),
  },
  {
    type: 2,
    icon: "&#xe6a4;",
    des: $c("viewPastConversations"),
  },
]);


const handleClick = (type: number) => {
  console.log('type', type)
  switch (type) {
    case 1:
      emit('showAddress')
      popoverVisible.value = false
      break;
    case 2:
      emit('showPastConversations')
      popoverVisible.value = false
      break;
    default:
      break;
  }
}

type ChatHeaderProps = {
  appCode: string;
  shouldCloseCompletely: boolean;
};
const props = withDefaults(defineProps<ChatHeaderProps>(), {
  appCode: "1",
  shouldCloseCompletely: false,
});

const csStatus = ref(-1);

// 发送客户状态消息
const sendCustomerStatusMessage = (eventType: number) => {
  const { status } = ws.getStatus()
  if ([1, 2].includes(status)) {
    console.log(`发送客户状态消息: eventType=${eventType}`)
    ws.sendMsg({ eventType }, { messageType: 91 })
  }
}

const hideChat = () => {
  if (props.shouldCloseCompletely) {
    closeChat();
  } else {
    // 发送组件最小化状态消息
    sendCustomerStatusMessage(2);
    $bus.emit("changeChatStatus", "minimize");
  }
};

const minimizeChat = () => {
  // 发送组件最小化状态消息
  sendCustomerStatusMessage(2);
  $bus.emit("changeChatStatus", "minimize");
};
const closeChat = () => {
  emit("closeChat");
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
