<template>
  <div
    class="live_chat_wrap"
    :class="isMobileBool ? 'isMobile' : ''"
    v-show="liveChatStatus"
  >
    <template v-if="!showPastConversations">
      <ChatHeader
        @close-chat="closeChat"
        :appCode="appCode"
        @show-address="showAddress"
        @show-past-conversations="showPastConversationsHandler"
        :shouldCloseCompletely="shouldCloseCompletely"
      ></ChatHeader>
      <section class="live_chat_body">
        <div
          class="live_chat_message_box"
          ref="messageBox"
        >
          <div
            class="chat_history_loading"
            v-if="historyLoadingStatus"
          >
            <GlobalLoading />
          </div>
          <ChatBody
            :message-list="formatMessageList"
            :app-code="appCode"
            :site-code="siteCode"
            @select-prod-qa="selectProdQa"
            @select-serve-type="selectServeType"
            @validate-video-click="videoClick"
          ></ChatBody>
          <!-- 正在输入效果 -->
          <div style="height: 18px;">
            <Transition name="focus-blur">
              <div
                class="typing_wrap"
                v-show="typingStatus"
              >
                <span class="typing_text">{{ $c('typing') }}</span>
              </div>
            </Transition>
          </div>
          <!-- 评价弹框 -->
          <!-- <div
            class="rate_feedback_box"
            v-if="showFeedbackBox"
          >
            <div class="rate_box">
              <p class="rate_title">{{ $c("feedbackTitle") }}</p>
              <div class="rate_btn_box">
                <FsButton
                  class="rate_btn"
                  :class="{ rate_btn_active: feedbackForm.score === 1 }"
                  type="whiteline"
                  @click="handleRate(1)"
                >
                  <span class="iconfont iconfont_good">&#xf026;</span>{{ $c("good") }}
                  <div
                    class="rate_tip"
                    v-if="feedbackForm.score === 1"
                  >{{ $c("rateTip") }}</div>
                </FsButton>
                <FsButton
                  class="rate_btn"
                  :class="{ rate_btn_active: feedbackForm.score === 2 }"
                  type="whiteline"
                  @click="handleRate(2)"
                >
                  <span class="iconfont iconfont_bad">&#xf027;</span>{{ $c("bad") }}
                  <div
                    class="rate_tip"
                    v-if="feedbackForm.score === 2"
                  >{{ $c("rateTip") }}</div>
                </FsButton>
              </div>
              <div
                class="has_voted"
                v-if="feedbackOption.hasVote"
              ><span class="iconfont iconfont_success">&#xf060;</span>{{ $c("hasVoted") }}</div>
            </div>
            <div class="feedback_box">
              <p class="feedback_title">{{ $c("commentTitle") + " " + `(${$c("formLabel.Optional")})` }}</p>
              <textarea
                class="feedback_textarea"
                id=""
                cols="30"
                maxlength="500"
                rows="10"
                :placeholder="$c('ratePlaceholder')"
                v-model="feedbackForm.comments"
              ></textarea>
              <div
                class="sg_email_input"
                v-if="siteCode === 'sg' && feedbackOption.showEmailInput"
              >
                <p class="feedback_title">Please leave an email address so we can reach you. (optional)</p>
                <input
                  type="text"
                  v-model="feedbackForm.email"
                  placeholder="Enter email here..."
                />
                <ValidateError :error="leaveMessageError.email"></ValidateError>
              </div>
              <FsButton
                type="black"
                :disabled="!feedbackOption.hasVote"
                @click="handleFeedback"
              >{{
                $c("submit")
                }}</FsButton>
            </div>
          </div> -->
        </div>
        <div
          class="back_bottom_wrap"
          @click="backToBottom"
        >
          <div
            class="back_icon"
            v-show="showBackIcon"
          >
            <i class="iconfont icon">&#xf049;</i>
          </div>
          <!-- <div class="msg_back_icon" v-show="showBackIcon && showMsgBackIcon">
                  <div>
                      <i class="iconfont icon">&#xf189;</i>
                      <span v-if="site_code === 'jp'">{{ $c("components.LiveChat.message").replace("$a", msgBackNum) }}</span>
                      <span v-else>{{ msgBackNum }} {{ msgBackNum > 1 ? $c("components.LiveChat.messages") : $c("components.LiveChat.message") }}</span>
                  </div>
              </div> -->
        </div>
      </section>
      <ChatFooter
        v-if="!isConversationPreview"
        @change-mute="changeMute"
        @send-message="sendMessage"
        @show-address="showAddress"
        @on-preview-msg="onPreviewMsg"
        @on-feedback-click="onFeedbackClick"
        :app-code="appCode"
        :site-code="siteCode"
        :message-list="messageList"
        :footer-disable="footerDisable"
        :newFeedbackIcon="newFeedbackIcon"
      >
      </ChatFooter>
    </template>
    
    <div v-else class="past-conversations-container">
      <PastConversations
        @back-to-chat="hidePastConversations"
        @select-conversation="selectConversation"
      />
    </div>

    <!-- 弹出评价弹框 -->
    <Popup
      :show="openPopup"
      @close="handleNewFeedbackClose"
    >
      <div class="feedback_container">
        <div class="feedback_head">
          <div class="feedback_title">{{ $c("newFeedback.title") }}</div>
          <div
            class="feedback_close"
            @click="handleNewFeedbackClose"
          >
            <span class="iconfont iconfont_close">&#xf30a;</span>
          </div>
        </div>
        <div class="feedback_body">
          <div class="rate_box">
            <span
              class="iconfont "
              :class="{ 'iconfont_good': newFeedbackForm.score === 1}"
              @click="handleNewRateClick(1)"
            >&#xe719;</span>
            <span
              class="iconfont"
              :class="{ 'iconfont_bad': newFeedbackForm.score === 2}"
              @click="handleNewRateClick(2)"
            >&#xe722;</span>
          </div>
          <input
            class="feedback_input"
            :placeholder="$c('newFeedback.comment')"
            v-model="newFeedbackForm.comments"
          />
          <FsButton
            :disabled="!newFeedbackOption.submitBtn"
            type="black"
            @click="handleNewFeedbackSubmit"
          >{{ $c("newFeedback.update") }}</FsButton>
        </div>
      </div>
    </Popup>


    <div class="live_chat_global_loading">
      <GlobalLoading v-if="globalLoadingStatus"></GlobalLoading>
    </div>
    <div
      class="live_chat_mask"
      v-show="showConfirmEndChat || emailOption.show_address_modal || showVideoTip"
    ></div>
    <div
      class="end_chat_box"
      v-show="showConfirmEndChat"
    >
      <div class="end_chat_icon_wrap">
        <span
          class="iconfont end_chat_close"
          @click.stop="hideEndChat"
        >&#xe643;</span>
      </div>
      <p class="end_chat_title">{{ $c("endChat") }}</p>
      <p class="end_chat_desc">{{ $c("closeTips") }}</p>
      <FsButton
        type="black"
        @click="confirmCloseClick"
      >{{ $c("closeBtn") }}</FsButton>
    </div>
     <div
      class="video_tip"
      v-show="showVideoTip"
    >
     <div class="title_wrap">
      <div class="title">{{ $c("videoTip.title") }}</div>
      <i class="iconfont iconfont_close" @click="closeVideoTip">&#xe6a0;</i>
     </div>
     <p class="content">{{ $c("videoTip.text") }}</p>
     <div class="btn_wrap">
       <FsButton
         type="black"
         @click="confirmStartVideo"
       >{{ $c("videoTip.btn") }}</FsButton>
     </div>
    </div>

    <!--下载聊天记录，填写邮件地址 -->
    <div
      class="address_modal"
      v-show="emailOption.show_address_modal"
    >
    <div class="title_wrap">
      <div class="title">{{ $c("emailTranscript") }}</div>
      <div class="icon_wrap">
        <i class="iconfont iconfont_close" @click.stop="addressModelClose">&#xf30a;</i>
      </div>
     </div>

      <!-- <span
        class="iconfont address_modal_close"
        @click.stop="addressModelClose"
      >&#xf041;</span>
      <div class="title_wrap">
        <div class="title">{{ $c("emailTranscript") }}</div>
      </div> -->
      <div class="address_modal_content">
        <template v-if="!emailOption.show_success_modal">
          <input
            type="text"
            v-model="emailOption.address"
          />
          <div
            v-show="emailOption.emailError"
            class="error"
          > <i class="iconfont error_icon">&#xe718;</i>  {{ emailOption.emailError }}</div>
          <div class="tips_wrap">
            {{ $c("emailTip") }}
          </div>
          <FsButton
            type="black"
            @click="emailSend"
            :loading="emailOption.sendLoading"
          >{{ $c("sendBtn") }}</FsButton>
        </template>
  
        <template v-else>
          <div class="info">
            <img src="@/assets/svg/email_sent.svg" />
            <span>{{ $c("sendStatus") }}</span>
          </div>
          <div
            class="tips_wrap"
            style="text-align: center"
          >{{ `${$c("sendInfoStart") + emailOption.address.trim() +
            $c("sendInfoEnd")}` }}</div>
          <FsButton
            type="blackline"
            @click="addressModelClose"
            class="back_btn"
          >{{ $c("backChat") }}</FsButton>
        </template>
      </div>
    </div>
    <!-- <audio
      src="https://resource.fs.com/mall/media/sound.mp3"
      hidden
      ref="audio"
    ></audio> -->
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch, unref, onBeforeUnmount, inject, getCurrentInstance } from 'vue';
import { formatDate, generateUUID, utcToLocal, debounce, urlToLink, xssFilter, validateEmail, getQueryString, parseJWT, throttle, isMobile, dataLayerToParent, filterSensitiveWords, getDeviceType } from '@/util/util';
import { post } from '@/util/request'
import { initSocket } from '@/plugins/socket'
import { AESCrypto } from '@/util/crypto';
// import { $c } from '@/plugins/c-inject';
import EventBus from '@/util/eventBus';

import ChatFooter, { MessagePayload } from './components/ChatFooter/index.vue';
import ChatHeader from './components/ChatHeader/index.vue';
import ChatBody from './components/ChatBody/ChatBody.vue';
import FsButton from '@/components/FsButton.vue';
import GlobalLoading from '@/components/GlobalLoading.vue';
import ValidateError from '@/components/ValidateError.vue';
import Popup from '@/components/Popup.vue';
import PastConversations from './components/PastConversations/index.vue';

const $c = getCurrentInstance()?.proxy.$c


type LiveChatProps = {
  liveChatStatus: boolean,
  originUrl?: string,
  productInfo?: {
    [key: string]: any
  }
}
const props = withDefaults(defineProps<LiveChatProps>(), {
  liveChatStatus: false,
  originUrl: '',
  productInfo: () => ({})
})
const formatMessageList = computed(() => {
  return messageList.value.map(item => {
    item.messageTime = utcToLocal(item.messageTime),
      item.msg = urlToLink(item.msg)
    item.messageReadStatus = ref(unref(item.messageReadStatus))
    return item
  })
})
const $bus = inject<EventBus>('eventBus')
const emit = defineEmits(['changeChatStatus'])
const siteCode = getQueryString('webSite') || 'en'
const appCode = getQueryString('appId') || '' // 1: 商城  2: APP  3: livechat后台管理系统
const ws = initSocket()
ws.on('message', handleMessage)
let feedbackInfo = {
  customerServiceId: '',
  groupManageId: 0
}

let customerServiceTypeId = -1

let hasCustomerServiceToRoom: string | number | NodeJS.Timeout = null

const csStatus = ref(-1)
const showMenuPop = ref(false)
const messageList = ref([])
const messageBox = ref<HTMLDivElement>()
const audio = ref<HTMLAudioElement>()
const defaultMsgBody = {
  messageType: 2,
  messageVisibilityType: 1,
  messageReadStatus: ref(false)
}
const feedbackForm = reactive({
  score: 0,
  comments: '',
  email: '',
})
const newFeedbackForm = reactive({
  score: 0,
  comments: '',
})
const newFeedbackOption = reactive({
  submitBtn: false,
  hasVote: false,
})
    
const feedbackOption = reactive({
  submitBtn: false,
  hasVote: false,
  showEmailInput: false
})
const leaveMessageError = reactive({
  name: '',
  email: '',
  leaveMsg: '',
  mobile: ''
})
const emailOption = reactive({
  show_address_modal: false,
  address: "",
  show_success_modal: false,
  emailError: '',
  sendLoading: false,
})
const stepRecord = ref(0)
const showFeedbackBox = ref(false)
const showConfirmEndChat = ref(false)
const globalLoadingStatus = ref(false)
const historyLoadingStatus = ref(false)
const historyPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

const footerDisable = ref(false)
const fromUrl = ref(props.originUrl)
const showBackIcon = ref(false)
const isMobileBool = ref(isMobile())
const typingStatus = ref(false)

// 新增属性：判断是否应该完全关闭组件
const shouldCloseCompletely = computed(() => {
  // 检查消息列表中是否有 messageType 为 87 的消息
  const hasType87Message = messageList.value.some(msg => msg.messageType === 87)
  if (!hasType87Message) return false

  // 如果有 messageType 为 87 的消息，检查表单是否已提交成功
  // footerDisable 为 true 表示表单还没有提交成功，此时应该完全关闭组件
  // footerDisable 为 false 表示表单已提交成功或可以提交，此时应该最小化
  return footerDisable.value
})


//新的评价弹框逻辑代码块
const newFeedbackIcon = ref(false)
const openPopup = ref(false);
// ws.on('open', joinRoom)
// function joinRoom () {
//   console.log('joinRoom')
// }
const onFeedbackClick = () => {
  openPopup.value = true;
}
const resetNewFeedbackForm = () => {
  newFeedbackForm.score = 0
  newFeedbackForm.comments = ''
  newFeedbackOption.hasVote = false
  newFeedbackOption.submitBtn = false
}
const handleNewFeedbackClose = () => {
  openPopup.value = false;
  resetNewFeedbackForm();
}
const handleNewRateClick = (score: number) => {
  newFeedbackForm.score = score
  newFeedbackOption.hasVote = true
  newFeedbackOption.submitBtn = true
}
const handleNewFeedbackSubmit = () => {
  ws.sendMsg({ ...newFeedbackForm, ...feedbackInfo }, { messageType: 40 })
  dataLayerToParent({
    eventLabel: `Submit Success`
  })
  // messageList.value.push({
  //   messageType: 42,
  //   clientMessageId: generateUUID(),
  //   comments: newFeedbackForm.comments,
  //   score: newFeedbackForm.score,
  // })
  handleNewFeedbackClose();
  scrollToBottom();
}
onMounted(() => {
  console.log('-----挂载')
  if (productInfo.value) {
    setTimeout(() => {
      createProductCardMsg(productInfo.value)
    }, 2000)
  }
  init()
  onNetworkStatus()
  messageBox.value?.addEventListener('scroll', scrollMessage)
  fetchHistoryMessage() // 请求历史消息
  window.addEventListener('message', handleUrlChange)
  window.addEventListener('resize', () => {
    isMobileBool.value = isMobile()
  })
  document.addEventListener('visibilitychange', pageVisibilityChange)

  // 添加客户状态展示功能的事件监听器
  setupCustomerStatusListeners()
})



onBeforeUnmount(() => {
  console.log('---卸载')
  messageBox.value?.removeEventListener('scroll', scrollMessage)
  window.removeEventListener('message', handleUrlChange)
  window.removeEventListener('resize', () => {})
  document.removeEventListener('visibilitychange', pageVisibilityChange)

  // 清理客户状态展示功能的事件监听器
  cleanupCustomerStatusListeners()
})
const init = () => {
  const { status } = ws.getStatus()
  if (![1, 2].includes(status)) {
    console.log('-----createWebSocket------')
    // 在创建新连接前重置连接状态，确保isReconnected为false
    ws.resetConnectionState()
    ws.createWebSocket()
  }
  dataLayerToParent({
    eventLabel: `chat started`,
  })
}

const pageVisibilityChange = () => {
  console.log('pageVisibilityChange-document.hidden: ', document.hidden);
  console.log('status', ws.getStatus());
  if (!document.hidden) {
    const { status } = ws.getStatus()
    if (![1, 2].includes(status)) {
      // 页面重新可见时，如果需要重连，先重置状态
      ws.resetConnectionState()
      ws.reconnect({ isForce: true })
     fetchHistoryMessage();
    } else {
      batchHandleUnreadMsg()
    }
    // 发送页面显示状态消息
    sendCustomerStatusMessage(7)
  } else {
    // 发送页面隐藏状态消息
    sendCustomerStatusMessage(5)
  }
}

// 客户状态展示功能相关函数
const sendCustomerStatusMessage = (eventType: number) => {
  const { status } = ws.getStatus()
  if ([1, 2].includes(status)) {
    console.log(`发送客户状态消息: eventType=${eventType}`)
    ws.sendMsg({ eventType }, { messageType: 91 })
  }
}

// 鼠标进入组件区域
const handleMouseEnter = () => {
  sendCustomerStatusMessage(3)
}

// 鼠标离开组件区域
const handleMouseLeave = () => {
  sendCustomerStatusMessage(4)
}

// 设置客户状态监听器
const setupCustomerStatusListeners = () => {
  const liveChatWrap = document.querySelector('.live_chat_wrap')
  if (liveChatWrap) {
    liveChatWrap.addEventListener('mouseenter', handleMouseEnter)
    liveChatWrap.addEventListener('mouseleave', handleMouseLeave)
  }
}

// 清理客户状态监听器
const cleanupCustomerStatusListeners = () => {
  const liveChatWrap = document.querySelector('.live_chat_wrap')
  if (liveChatWrap) {
    liveChatWrap.removeEventListener('mouseenter', handleMouseEnter)
    liveChatWrap.removeEventListener('mouseleave', handleMouseLeave)
  }
}


const productInfo = ref(props.productInfo)
const isProductPage = ref(false)
const showVideoTip = ref(false)
const prod_record = ref()
const handleUrlChange = (e: MessageEvent) => {
  console.log('urlChange', e.data)
  if (e.data.type === 'urlChange') {
    fromUrl.value = e.data.origin
    isProductPage.value = false;
  }
  if (e.data?.type === 'prod_card') {
    createProductCardMsg(e.data.data)
  }
  if (e.data?.type === 'prod_page_enter') {
    console.log('prod_page_enter', e.data.data?.is_product_detail);
    isProductPage.value = true;
  }
}
const videoClick = (item: any) => {
  console.log('videoClick', isProductPage.value, item);
  if (!isProductPage.value) {
    showVideoTip.value = true;
    prod_record.value = item
  }
}
const confirmStartVideo = () => {
    window.parent.postMessage({ type: 'validate-video-click', data: { prod_id: prod_record.value?.id } }, '*')
}

const closeVideoTip = () => {
  showVideoTip.value = false;
}

// 创建产品卡片消息
const createProductCardMsg = (data:any) => {
  const { productsBaseInfo, questionAnswerList } = data
  const msgParams = {
    questionVo: {
      ...productsBaseInfo,
      questionAnswerList,
    },
    formType: 1
  }
  const msgBody = {
    formType: 1,
    extraData: msgParams.questionVo,
    messageType: 76
  }
  console.log('-------productCard', msgBody)
  ws.sendMsg(msgParams, {messageType: 75})
  messageList.value.push(msgBody)
  scrollToBottom()
}
// 匹配链接
const extractUrl = (text: string) => {
  // 匹配所有a标签
  // 如果有a标签，将a标签内的内容替换为链接
  // 如果没有a标签，将所有链接替换为a标签
  if (text.includes('<a') && text.includes('</a>')) {
    /* eslint-disable */
    const aReg = /<a\b((?![^>]*\btarget=["']?_blank["']?)[^>]*)>/gi
    const modifyText = text.replace(aReg, '<a$1 target="_blank">')
    return modifyText
  } else {
    /* eslint-disable */
    const UrlReg = /((http|https|ftp):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?)/gi
    let s = text.replace(UrlReg, `<a href='$1' target="_blank">$1</a>`)
    return s
  }
}
const selectProdQa = (item:any) => {
  const {content, answer, id} = item
  //展示所用消息体
  const msgBody = {
    msg: content,
    messageType: 32,
    messageVisibilityType: 1,
    messageReadStatus: true,
    messageTime: formatDate(new Date().getTime(), 'en'),
    clientMessageId: generateUUID()
  }
  //发送到服务端消息体
  const msgParams = {
    formType: 2,
    questionAnswerVo: {
      id,
      content,
      answerList: answer
    }
  }
  messageList.value.push(msgBody)
  //request用75发送
  ws.sendMsg(msgParams, {messageType: 75})
  scrollToBottom()
  dataLayerToParent({
    eventLabel: `DetailQuestion_${content}_${id}`,
  })
}
const onNetworkStatus = () => {
  ws.on('online', () => {
    console.log('网络恢复')
  })
  ws.on('offline', () => {
    console.log('网络断开')
  })
}
const clearChatStorage = () => {
  localStorage.removeItem('live_chat_group_id')
}

const sendReadMsgRequest = (msg: MessageBody) => {
  if (msg.messageType === 21) {
    ws.sendMsg({ type: 4, messageId: msg.messageId, clientMessageId: msg.clientMessageId, groupId: msg.groupId, fromUserId: msg.fromUserId }, { messageType: 34 })
  }
}
const batchHandleUnreadMsg = () => {
  const unreadMsg = messageList.value.filter(item => item.messageType === 21 && !item.messageReadStatus)
  console.log('unreadMsg', unreadMsg);
  if (unreadMsg.length) {
    unreadMsg.forEach(item => {
      sendReadMsgRequest(item)
    })
  }
}
/*
* 延迟展示消息
*/
const delayDisplayMessage = (msg: MessageBody) => {
  setTimeout(() => {
    msg.isLoading.value = false
    scrollToBottom()
  }, 800)
}
/**
 * @description: onMessage回调函数，接受到消息并处理逻辑
 * @param {*} msg
 * @return {*}
 */
function handleMessage(msg: MessageBody) {
  const { messageVisibilityType, messageType } = msg
  if ([1,2].includes(messageVisibilityType)) { //全部可见消息显示
    setUnreadMessageStatus(true);
    handleSoundNotice(messageType)
    msg.isLoading = ref(true)

    // 如果当前正在查看历史聊天记录，不将实时消息添加到消息列表中
    if (!isConversationPreview.value) {
      messageList.value.push(msg)
      scrollToBottom()
      delayDisplayMessage(msg)
    }

    sendReadMsgRequest(msg)
  }
  messageTypeProcessing(messageType, msg)
}


const messageTypeProcessing = (type: number, msg: MessageBody) => {
  if (type === 35) {
    window.localStorage &&
      window.localStorage.setItem('live_chat_userId', msg.uuid)
    // 存储id
    localStorage.setItem('live_chat_group_id', msg.groupStr)
    //批量处理未读消息
    batchHandleUnreadMsg()
  } else if (type === 46) { // 进入邮箱留资步骤
    stepRecord.value = 1
    localStorage.setItem('register_info', JSON.stringify({ step: 1, email: '' }))
  } else if (type === 47) {
    stepRecord.value = 2
    const data = JSON.parse(localStorage.getItem('register_info'))
    data.step = 2
    localStorage.setItem('register_info', JSON.stringify(data))
  } else if (type === 41) {
    if (siteCode === 'sg') {
      const userInfo = parseJWT()
      if (userInfo?.email?.length === 0) {
        feedbackOption.showEmailInput = true
      }
    }
    showFeedbackBox.value = true
    if (msg.groupManageId) {
      feedbackInfo = {
        customerServiceId: msg.fromUserId,
        groupManageId: msg.groupManageId
      }
    }
    scrollToBottom()
  } else if (type === 15) {
    stepRecord.value = 0
    localStorage.removeItem('register_info')
  } else if ([30, 37].includes(type)) { // 关闭聊天
    clearChatStorage()
    ws.close()
    emit('changeChatStatus', 'close')
    sendCustomerStatusMessage(1)
  } else if (type === 52 && msg.type === 5) { //隐藏标签
    const index = messageList.value.findIndex(item => item.messageId === msg.clientMessageId)
    nextTick(() => {
      hideSelfSerive(index)
    })
  } else if (type === 43) {
    if (showFeedbackBox.value) return showFeedbackBox.value = false
    const leaveMsgItem = messageList.value.find(item => item.messageType === 54)
    if (leaveMsgItem) {
      leaveMsgItem.messageType = 51
      leaveMsgItem.requestType = ref(54)
      return
    }
    // const emailMsgItem = messageList.value.find(item => item.messageType === 49)
    // if (emailMsgItem) {
    //   emailMsgItem.messageType = 51
    //   emailMsgItem.requestType = ref(49)
    //   emailMsgItem.suffixStatus = 1
    //   return
    // }
    const applyForm = messageList.value.find(item => item.messageType === 64)
    if (applyForm && applyForm.formType === 1) {
      applyForm.messageType = 51
      applyForm.requestType = ref(64)
      return
    } else {
      applyForm.messageType = 51
      applyForm.requestType = ref(64)
      applyForm.suffixStatus = 1
    }
  } 
  // else if (type === 49) {
  //   // msg.email = ref(msg.email)
  //   // msg.isShowEmail = ref(msg.email ? false : true)
  //   // msg.suffixStatus = ref(3)
  // } 
  else if (type === 54) {
    footerDisable.value = true
    msg.leaveMsg = ref('')
    msg.email = ref(msg.email)
    msg.mobile = ref(msg.mobile)
    msg.customerName = ref(msg.customerName)
    // 没有记录，展示输入框
    msg.isShowEmail = ref(unref(msg.email) ? false : true)
    msg.isShowName = ref(unref(msg.customerName) ? false : true)
    msg.isShowMobile = ref(unref(msg.mobile) ? false : true)
  } else if (type === 52 && msg.type === 4) {
    //消息已读状态改变
    const index = messageList.value.findIndex(item => item.clientMessageId === msg.clientMessageId)
    if (index !== -1) {
      const item = messageList.value[index]
      item.messageReadStatus = true
    }
  } else if (type === 64) {
    // 上一个表单置灰
    const applyFormList = messageList.value.filter(item => [64].includes(item.messageType))
    if (applyFormList.length > 1) {
      const prevApplyForm = applyFormList[applyFormList.length - 2]
      prevApplyForm.messageType = 51
      prevApplyForm.requestType = ref(64)
      console.log(prevApplyForm)
    }
    if (msg.formType === 2) {
      msg.email = ref(msg.email)
      msg.isShowEmail = ref(msg.email ? false : true)
      msg.suffixStatus = ref(3) 
      msg.applyError = ref<ApplyFormError>({
        email: ''
      })
    } else {
      msg.customerName = ref('')
      msg.email = ref('')
      msg.mobile = ref('')
      msg.applyError = ref<ApplyFormError>({
        mobile: '',
        name: ''
      })
    }
  } else if (type === 72) {
    csStatus.value = msg.isOnline ? 1 : 0
  } else if ([26, 48].includes(type)) {
    // 客服接入成功
    $bus.emit('showEndBtn', true)
    window.localStorage.setItem('show_end_btn', 'true')
    dataLayerToParent({
      eventLabel: `agent service_available`
    })
  } else if (type === 81) {
    if (isConversationPreview.value) return;
    typingStatus.value = msg.hasTyping
  } else if (type === 85) {
    console.log('query customer service status', msg.hasCustomerServiceToRoom);
    newFeedbackIcon.value = msg.hasCustomerServiceToRoom
    // if (!msg.hasCustomerServiceToRoom) {
    //   if (showFeedbackBox.value) {
    //     ws.sendMsg({ isForceClose: true, customerServiceId: feedbackInfo.customerServiceId }, { messageType: 36 })
    //   }
    //   clearTimeout(hasCustomerServiceToRoom)
    //   clearChatStorage()
    //   ws.close()
    //   showFeedbackBox.value = false
    //   emit('changeChatStatus', 'close')
    // }
  } else if (type === 87) {
    footerDisable.value = true
    $bus.on('user-form-submit-success', () => {
      footerDisable.value = false
    })
  } else if (type === 89) { // 客服接入失败
    dataLayerToParent({
      eventLabel: `agent service_unavailable`
    })
  }
}
const onPreviewMsg: (text: string) => void = debounce((text: string) => {
  ws.sendMsg({ msg: text, url: fromUrl.value }, { messageType: 65 })
}, 500)


/**
 * 
 * @description: 移动端输入框聚焦消息滚动到底部
 */

 const mobileInputFocus = () => {
  setTimeout(() => {
    backToBottom()
  }, 300)
 }

const backToBottom = () => {
  messageBox.value.scrollTop = messageBox.value.scrollHeight
}
$bus.on('mobileInputFocus', mobileInputFocus)


//向上滚动聊天记录，请求历史消息
const scrollMessage = throttle((e: Event) => {
  const target = e.target as HTMLDivElement
  if (target.scrollTop < 10 && !historyLoadingStatus.value) {
    if (historyPagination.page * historyPagination.pageSize < historyPagination.total) {
      historyPagination.page++
      historyLoadingStatus.value = true
      if (isConversationPreview.value) {
        fetchConversation(currentConversation.value)
      } else {
        fetchHistoryMessage()
      }
    }
  }
  if (target.scrollTop <= target.scrollHeight - target.clientHeight - 500) {
    showBackIcon.value = true
  } else {
    showBackIcon.value = false
  }
}, 500)

// 请求历史记录
const fetchHistoryMessage = () => {
  if (!localStorage.getItem('live_chat_userId')) return globalLoadingStatus.value = false
  // const cryptoUserId = AESCrypto.encrypt(localStorage.getItem('live_chat_userId'), '-feisu_chat_0823', 'fs-livechat-2308')
  const cryptoUserId = localStorage.getItem('live_chat_userId');
  const params = {
    current: historyPagination.page,
    size: historyPagination.pageSize,
    appId: 1,
    search: cryptoUserId,
  }
  if (appCode === '2') {
    globalLoadingStatus.value = true
    post('/livechat/message/customer/list', {
      ...params
    }).then((res: any) => {
      console.log(res)
      const currentTop = messageBox.value?.scrollHeight
      historyLoadingStatus.value = false
      globalLoadingStatus.value = false
      messageList.value.unshift(...res.records)
      historyPagination.total = res.total
      historyPagination.page = res.current
      historyPagination.pageSize = res.size
      if (historyPagination.page === 1) {
        getQueryString('isoCode') === 'SG' && messageList.value.push({
          messageType: 123
        })
        scrollToBottom()
      } else {
        nextTick(() => {
          messageBox.value.scrollTop = messageBox.value.scrollHeight - currentTop
        })
      }
    }).finally(() => {
      historyLoadingStatus.value = false
      globalLoadingStatus.value = false
      nextTick(() => {
        batchHandleUnreadMsg()
      })
    })
  } else {
    fetchConversation(currentConversation.value)
  }
}
/**
 * @description: 发送消息
 * @return {*}
 */
const sendMessage = (val: MessagePayload) => {
  const { text, files } = val
  if (!text && !files.length) return
  let filterValue = text
  let hasSensitive = false
  if (['cn', 'hk', 'mo', 'tw'].includes(siteCode)) {
    const {result, hasSensitiveWords} = filterSensitiveWords(text)
    filterValue = result
    hasSensitive = hasSensitiveWords
  }
  const formatTextareaValue = xssFilter(filterValue)
  const hideMsgList = checkHideSelfServe()
  let messageType = 2
  let msgBody: { [key: string]: any } = {
    messageTime: formatDate(new Date().getTime(), 'en'),
    ...defaultMsgBody
  }
  let msgParams: Record<string, any> = { clientMessageId: generateUUID() }
  const registerInfo = JSON.parse(localStorage.getItem('register_info'))
  // 文本消息
  if (text && !files.length) {
    msgBody.msg = formatTextareaValue
    if (registerInfo?.step) {
      stepRecord.value = registerInfo.step
    }
    if (stepRecord.value === 1) { //留资步骤1
      messageType = 23
      msgParams.email = formatTextareaValue
      localStorage.setItem('register_info', JSON.stringify({ step: stepRecord.value, email: msgParams.email }))
      ws.sendMsg(msgParams, { messageType })
    } else if (stepRecord.value === 2) { //留资步骤2
      messageType = 24
      msgParams.name = formatTextareaValue
      msgParams.email = registerInfo.email
      msgParams.customerServiceTypeId = customerServiceTypeId
      ws.sendMsg(msgParams, { messageType })
    } else {
      msgParams = {
        groupId: '',
        msg: formatTextareaValue,
        ...msgParams
      }
      hideMsgList ? msgParams.hideMsgList = hideMsgList : ''
      ws.sendMsg(msgParams, { messageType })
    }
    $bus.emit('clearTextValue')
    messageList.value.push({ ...msgBody, ...msgParams })
    if (hasSensitive) {
      let msgBody = {
        msg: '请注意输入内容中存在敏感词汇，已脱敏处理。',
        messageType: 82
      }
      ws.sendMsg(msgBody, { messageType: 82 })
      messageList.value.push(msgBody)
    }
  }
  // 文件消息
  if (files.length) {
    
    files.forEach(item => {
      let params = {
        fileInfo: {
          ...item
        },
        clientMessageId: generateUUID()
      }
      ws.sendMsg(params, { messageType })
      messageList.value.push({ ...msgBody, ...params })
    })
    files.splice(0, files.length)
  }
  scrollToBottom()
}



// 检查是否隐藏自服务标签 
const checkHideSelfServe = () => {
  const filterList = messageList.value.filter(item => [19, 45].includes(item.messageType))
  if (filterList.length > 0) {
    const hideMsgList = filterList.map(item => String(item.messageId))
    return hideMsgList
  }
}


/**
 * @description: 选择自服务标签
 * @param {*} menuItem
 * @param {*} any
 * @param {*} messageId
 * @param {*} msgType
 * @param {*} index
 * @return {*}
 */
const selectServeType = (menuItem: ServeType, messageId: string, msgType: number, index: number): any => {
  const { id, level, type, content, grade } = menuItem
  const clientMessageId = generateUUID()
  if (msgType === 45) {
    customerServiceTypeId = id
  }
  let params = {
    messageId,
    id,
    level,
    type,
    msg: content,
    clientMessageId
  }
  let msgBody = {
    msg: content,
    messageTime: formatDate(new Date().getTime(), 'en'),
    clientMessageId,
    ...defaultMsgBody
  }
  messageList.value.push(msgBody)
  scrollToBottom()
  ws.sendMsg(params, { messageType: msgType === 19 ? 18 : 28 })
  handleServeGa(level, content, id, grade)
}

/**
 * @description: 自服务标签埋点
 */
const handleServeGa = (level: number, content: string, id: number, grade: number) => {
  let eventLabel = ''
  switch (level) {
    case 1:
     eventLabel = `First_${content}_${id}`
      break;
    case 2:
      eventLabel = `Second_${content}_${id}`
      break;
    case 3:
      eventLabel = `Question_${content}_${id}`
      break;
    default:
      if (id === -1) {
        if (grade === 1) {
          eventLabel = "First_contact agent"
        } else if (grade === 2) {
          eventLabel = "Second_contact agent"
        } else if (grade === 3) {
          eventLabel = "Question_contact agent"
        } else {
          eventLabel = 'contact agent'
        }
      } else if (id === -2) {
        eventLabel = "is solved"
      } else if (id === -3) {
        eventLabel = 'start over'
      } else {
        const prod_id = [1,3,5,7,9,11,13,15,17,19,21,23,25,27,29,31,33].includes(id)
        const technical_id = [2,4,6,8,10,14,16,18,20,22,24,26,28,30,32,34].includes(id)
        if (prod_id) {
          eventLabel = `select agent_product purchase`
        }
        if (technical_id) {
          eventLabel = `select agent_technical support`
        }
      }
      break;
  }
  dataLayerToParent({
    eventLabel
  })
}
const scrollToBottom = () => {
  nextTick(() => {
    if (messageBox.value) {
      const messageEl = messageBox.value
      if (!showBackIcon.value) {
        messageEl.scrollTo({
          top: messageEl.scrollHeight,
          behavior: "smooth",
        })
      }
    }
  })
}

const hideSelfSerive = (index: number) => {
  messageList.value.splice(index, 1)
}
const hideEndChat = () => {
  showConfirmEndChat.value = false
}
const confirmCloseClick = () => {
  ws.sendMsg({ isForceClose: false, customerServiceId: feedbackInfo.customerServiceId }, { messageType: 36 })
  showConfirmEndChat.value = false
}

/**
 * @description: 提交评价反馈
 */
const handleFeedback = () => {
  if (feedbackForm.email) {
    if (!validateEmail(feedbackForm.email)) {
      return leaveMessageError.email = $c('formError.email.error2')
    }
    const params = {
      email: feedbackForm.email,
      requestType: 40,
    }
    ws.sendMsg(params, { messageType: 50 })
  }
  ws.sendMsg({...feedbackForm, ...feedbackInfo}, { messageType: 40 })
  feedbackOption.submitBtn = true
  dataLayerToParent({
    eventLabel: `Submit Success`
  })
}

/**
 * @description: 好评差评
 * @param type 1:好评 2:差评
 * 
 */
const handleRate = (type: number) => {
  feedbackForm.score = type
  feedbackOption.hasVote = true
  dataLayerToParent({
    eventLabel: `${type === 1 ? `Good` : `Bad`}`
  })
}

const mobileEndChat = () => {
  closeChat()
}
$bus.on('mobileEndChat', mobileEndChat)

const closeChat = () => {
  // 发送会话结束状态消息
  sendCustomerStatusMessage(1)

  //当socket状态不是正常连接时，可以直接关闭
  if (![2].includes(ws.getStatus().status)) {
    clearChatStorage()
    ws.close()
    return emit('changeChatStatus', 'close')
  }
  ws.sendMsg({ isForceClose: true, customerServiceId: feedbackInfo.customerServiceId }, { messageType: 36 })
  clearChatStorage()
  ws.close()
  emit('changeChatStatus', 'close')
  // ws.sendMsg({}, {messageType: 84})
  // hasCustomerServiceToRoom = setTimeout(() => {
  //   if (showFeedbackBox.value) { // 强关闭
  //     ws.sendMsg({ isForceClose: true, customerServiceId: feedbackInfo.customerServiceId }, { messageType: 36 })
  //     clearChatStorage()
  //     ws.close()
  //     showFeedbackBox.value = false
  //     emit('changeChatStatus', 'close')
  //   } else {
  //     showConfirmEndChat.value = true
  //   }
  // }, 800)
}


let globalMuteStatus = false
const changeMute = (status: boolean) => {
  globalMuteStatus = status
}
// 声音通知
const handleSoundNotice = (type: number) => {
  if ([3, 20, 26, 30, 37, 43].includes(type)) return

  // 声音通知注释掉
  // if (audio.value && !globalMuteStatus) {
  //   audio.value.currentTime = 0
  //   const promise = audio.value.play()
  //   promise.catch(e => console.log(e))
  // }
}
// 设置未读消息状态
const setUnreadMessageStatus = (bool: boolean) => {
  // 组件隐藏设置未读消息状态
  if (!props.liveChatStatus) {
    window.parent.postMessage({type: 'unread_message', data: bool}, '*')
  }
}
const showAddress = () => {
  emailOption.show_address_modal = true
  showMenuPop.value = false
  dataLayerToParent({
    eventLabel: "Email transcript"
  })
}
const addressModelClose = () => {
  emailOption.show_address_modal = false
  emailOption.show_success_modal = false
  emailOption.address = ''
  emailOption.emailError = ''
}
const emailSend = () => {
  const params = {
    email: emailOption.address,
    // groupIdStr: localStorage.getItem('live_chat_group_id'),
    // search: AESCrypto.encrypt(localStorage.getItem('live_chat_group_id'), '-feisu_chat_0823', 'fs-livechat-2308'),
    search: localStorage.getItem('live_chat_group_id'),
    languagesId: 1,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  }
  if ( emailOption.address.trim() && !validateEmail(emailOption.address.trim())) {
    return emailOption.emailError = $c('formError.email.error2')
  } else if (emailOption.address.trim() === '') {
    return emailOption.emailError = $c('formError.email.error1')
  }  else {
    emailOption.emailError = ''
  }
  emailOption.sendLoading = true
  post('/livechat/email/send-chat-message-user', params).then((res: any) => {
    if (res.result) {
      emailOption.show_success_modal = true
      dataLayerToParent({
         eventLabel: `Send transcript_success`
      })
    }
  }).catch(e => {
    emailOption.emailError = 'Send transcript failed. Please try again.'
    dataLayerToParent({
        eventLabel: `Send transcript_fail`,
    })
  })
    .finally(() => emailOption.sendLoading = false)
}


const showPastConversations = ref(false);
const isConversationPreview = ref(false); // 是否是历史对话预览
const currentConversation = ref(null);
// 显示历史对话列表
const showPastConversationsHandler = () => {
  showPastConversations.value = true;
  //重置footerDisable
  footerDisable.value = false
};

// 隐藏历史对话列表
const hidePastConversations = (conversation: any) => {
  console.log('conversation', conversation);
  showPastConversations.value = false;

  // 如果没有传递conversation参数，说明是点击返回按钮回到当前聊天
  if (!conversation) {
    isConversationPreview.value = false;
    currentConversation.value = null;
    // 重新加载当前聊天的历史消息
    historyPagination.page = 1;
    historyPagination.total = 0;
    messageList.value = [];
    fetchHistoryMessage();
    return;
  }

  // groupIdStr不相同，是历史记录，相同则是正在聊天
  if (conversation.groupIdStr !== localStorage.getItem('live_chat_group_id')) {
    isConversationPreview.value = true;
  } else {
    isConversationPreview.value = false;
  }
};

// 选择历史对话
const selectConversation = (conversationData: any) => {
  console.log('选择历史对话', conversationData);

  // 从传入的数据中提取对话信息和是否为当前会话的标识
  const { isCurrentConversation, ...conversation } = conversationData;

  // 函数优化分析：
  // 1. selectConversation 函数不再需要总是调用 hidePastConversations
  // 2. 根据是否为当前会话来决定不同的处理逻辑，提升用户体验
  // 3. 避免不必要的组件显示/隐藏切换

  if (isCurrentConversation) {
    // 场景：用户选择的是当前正在进行的会话
    // 优化：直接返回到聊天界面，不需要进入预览模式
    showPastConversations.value = false;
    isConversationPreview.value = false;
    currentConversation.value = null;

    // 重新加载当前聊天的历史消息
    // fetchHistoryMessage 会调用 fetchConversation(null)，active 参数为 true
    historyPagination.page = 1;
    historyPagination.total = 0;
    messageList.value = [];
    fetchHistoryMessage();
  } else {
    // 场景：用户正在预览历史记录
    // 需要进入预览模式，调用 fetchConversation 时 active 参数为 false
    hidePastConversations(conversation);

    // 重置分页并加载历史对话
    historyPagination.page = 1;
    historyPagination.total = 0;
    messageList.value = []; // 清空当前消息列表
    currentConversation.value = conversation;
    footerDisable.value = false;

    // 调用 fetchConversation，active 参数会自动为 false（因为 groupIdStr 不相等）
    fetchConversation(conversation);
  }
};

// 获取历史列表详情
const fetchConversation = async (conversation: any) => {
  const currentGroupId = localStorage.getItem('live_chat_group_id');
  const conversationGroupId = conversation?.groupIdStr;

  const params = {
    current: historyPagination.page,
    size: historyPagination.pageSize,
    appId: 1,
    search: localStorage.getItem('live_chat_userId'),
    paragraph: conversationGroupId || currentGroupId,
    // 修复 active 参数逻辑：
    // 1. 如果 conversation 为 null（返回按钮场景），active 为 true
    // 2. 如果 conversation 存在，比较 groupIdStr 是否相等
    active: !conversation || conversationGroupId === currentGroupId
  };
  if (historyPagination.page === 1) {
    globalLoadingStatus.value = true;
  } else {
    historyLoadingStatus.value = true;
  }
  post('/livechat/group-detail/list', params)
    .then((res: any) => {
      console.log(res);
      const currentTop = messageBox.value?.scrollHeight;
      historyLoadingStatus.value = false;
      globalLoadingStatus.value = false;
      
      // 添加消息记录并更新分页信息
      if (historyPagination.page === 1) {
        messageList.value = [...res.records];
        nextTick(() => {
          scrollToBottom();
          // 如果内容不足以产生滚动，且有更多数据，手动请求下一页
          if (messageBox.value && 
              messageBox.value.scrollHeight <= messageBox.value.clientHeight && 
              res.total > res.records.length) {
            historyPagination.page++;
            fetchConversation(conversation);
          }
        });
      } else {
        messageList.value.unshift(...res.records);
        nextTick(() => {
          if (messageBox.value && currentTop) {
            messageBox.value.scrollTop = messageBox.value.scrollHeight - currentTop;
          }
        });
      }
      
      historyPagination.total = res.total;
      historyPagination.page = res.current;
      historyPagination.pageSize = res.size;
    })
    .finally(() => {
      historyLoadingStatus.value = false;
      globalLoadingStatus.value = false;
      nextTick(() => {
        batchHandleUnreadMsg();
      });
    });
    // 确保绑定滚动事件
  nextTick(() => {
    messageBox.value?.removeEventListener('scroll', scrollMessage);
    messageBox.value?.addEventListener('scroll', scrollMessage);
    // 手动触发一次初始加载
    if (messageBox.value) {
      messageBox.value.scrollTop = 0;
    }
  });
}
</script>

<style lang="scss" scoped>
@import '@/style/liveChat.scss';

.past-conversations-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  z-index: 10;
}
</style>